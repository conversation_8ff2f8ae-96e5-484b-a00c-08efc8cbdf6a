#!/bin/bash

# Kubernetes 集群通用配置脚本
# 在所有节点上运行此脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/redhat-release ]]; then
        OS="centos"
        log_info "检测到 CentOS/RHEL 系统"
    elif [[ -f /etc/lsb-release ]]; then
        OS="ubuntu"
        log_info "检测到 Ubuntu 系统"
    else
        log_error "不支持的操作系统"
        exit 1
    fi
}

# 配置主机名和 hosts
setup_hostname() {
    log_info "配置主机名和 hosts 文件..."
    
    echo "请选择节点类型："
    echo "1) Master 节点"
    echo "2) Worker 节点"
    read -p "请输入选择 (1-2): " node_type
    
    case $node_type in
        1)
            hostnamectl set-hostname k8s-master
            log_success "主机名设置为 k8s-master"
            ;;
        2)
            hostnamectl set-hostname k8s-worker
            log_success "主机名设置为 k8s-worker"
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
    
    # 获取当前 IP
    local_ip=$(ip route get ******* | awk '{print $7; exit}')
    log_info "检测到本机 IP: $local_ip"
    
    read -p "请输入 Master 节点 IP: " master_ip
    read -p "请输入 Worker 节点 IP: " worker_ip
    
    # 更新 hosts 文件
    cat >> /etc/hosts << EOF

# Kubernetes 集群节点
$master_ip k8s-master
$worker_ip k8s-worker
EOF
    
    log_success "hosts 文件配置完成"
}

# 关闭防火墙和 SELinux
disable_firewall_selinux() {
    log_info "关闭防火墙和 SELinux..."
    
    if [[ $OS == "centos" ]]; then
        systemctl stop firewalld 2>/dev/null || true
        systemctl disable firewalld 2>/dev/null || true
        setenforce 0 2>/dev/null || true
        sed -i 's/^SELINUX=enforcing$/SELINUX=permissive/' /etc/selinux/config 2>/dev/null || true
    elif [[ $OS == "ubuntu" ]]; then
        ufw disable 2>/dev/null || true
    fi
    
    log_success "防火墙和 SELinux 已关闭"
}

# 关闭 swap
disable_swap() {
    log_info "关闭 swap..."
    
    swapoff -a
    sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab
    
    log_success "swap 已关闭"
}

# 配置内核参数
setup_kernel_params() {
    log_info "配置内核参数..."
    
    cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
EOF

    cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
EOF

    modprobe br_netfilter
    sysctl --system
    
    log_success "内核参数配置完成"
}

# 安装 Docker
install_docker() {
    log_info "安装 Docker..."
    
    if command -v docker &> /dev/null; then
        log_warning "Docker 已安装，跳过安装步骤"
        return
    fi
    
    if [[ $OS == "centos" ]]; then
        yum install -y yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        yum install -y docker-ce docker-ce-cli containerd.io
    elif [[ $OS == "ubuntu" ]]; then
        apt-get update
        apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
        apt-get update
        apt-get install -y docker-ce docker-ce-cli containerd.io
    fi
    
    log_success "Docker 安装完成"
}

# 配置 Docker
configure_docker() {
    log_info "配置 Docker..."
    
    mkdir -p /etc/docker
    cat > /etc/docker/daemon.json << EOF
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m"
  },
  "storage-driver": "overlay2"
}
EOF

    systemctl enable docker
    systemctl start docker
    
    log_success "Docker 配置完成"
}

# 安装 Kubernetes 组件
install_kubernetes() {
    log_info "安装 Kubernetes 组件..."
    
    if [[ $OS == "centos" ]]; then
        cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://packages.cloud.google.com/yum/doc/yum-key.gpg https://packages.cloud.google.com/yum/doc/rpm-package-key.gpg
EOF
        yum install -y kubelet kubeadm kubectl --disableexcludes=kubernetes
    elif [[ $OS == "ubuntu" ]]; then
        curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -
        cat > /etc/apt/sources.list.d/kubernetes.list << EOF
deb https://apt.kubernetes.io/ kubernetes-xenial main
EOF
        apt-get update
        apt-get install -y kubelet kubeadm kubectl
        apt-mark hold kubelet kubeadm kubectl
    fi
    
    systemctl enable kubelet
    
    log_success "Kubernetes 组件安装完成"
}

# 主函数
main() {
    echo "========================================"
    echo "    Kubernetes 集群通用配置脚本"
    echo "========================================"
    echo
    
    check_root
    detect_os
    setup_hostname
    disable_firewall_selinux
    disable_swap
    setup_kernel_params
    install_docker
    configure_docker
    install_kubernetes
    
    echo
    echo "========================================"
    echo "           配置完成"
    echo "========================================"
    echo
    log_success "通用配置已完成！"
    echo
    echo "下一步："
    echo "- 如果这是 Master 节点，请运行: ./setup-master.sh"
    echo "- 如果这是 Worker 节点，请等待 Master 节点初始化完成后运行 join 命令"
    echo
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

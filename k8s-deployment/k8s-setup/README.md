# Kubernetes 集群搭建指南（内网环境）

本指南适用于在内网环境中使用两台服务器搭建 Kubernetes 集群。

## 🖥️ 服务器要求

### 最低配置
- **节点数量**：2 台服务器
- **操作系统**：CentOS 7.9+ / Ubuntu 18.04+ / RHEL 7+
- **内存**：每台 4GB+
- **CPU**：每台 2 核心+
- **磁盘**：每台 50GB+
- **网络**：内网互通

### 推荐配置
- **内存**：每台 8GB+
- **CPU**：每台 4 核心+
- **磁盘**：每台 100GB+（SSD 更佳）

## 🏗️ 架构规划

```
┌─────────────────┐    ┌─────────────────┐
│   Master Node   │    │   Worker Node   │
│  (k8s-master)   │    │  (k8s-worker)   │
│                 │    │                 │
│ • Control Plane │    │ • kubelet       │
│ • etcd          │    │ • kube-proxy    │
│ • API Server    │    │ • Container     │
│ • Scheduler     │    │   Runtime       │
│ • kubelet       │    │                 │
│ • kube-proxy    │    │                 │
└─────────────────┘    └─────────────────┘
        │                       │
        └───────────────────────┘
              内网连接
```

## 📝 准备工作

### 1. 服务器基础配置

在**所有节点**上执行以下操作：

#### 设置主机名和 hosts
```bash
# 节点1（Master）
hostnamectl set-hostname k8s-master

# 节点2（Worker）
hostnamectl set-hostname k8s-worker

# 在所有节点的 /etc/hosts 中添加
cat >> /etc/hosts << EOF
************ k8s-master
************ k8s-worker
EOF
```

#### 关闭防火墙和 SELinux
```bash
# CentOS/RHEL
systemctl stop firewalld
systemctl disable firewalld
setenforce 0
sed -i 's/^SELINUX=enforcing$/SELINUX=permissive/' /etc/selinux/config

# Ubuntu
ufw disable
```

#### 关闭 swap
```bash
swapoff -a
sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab
```

#### 配置内核参数
```bash
cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
EOF

cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
EOF

modprobe br_netfilter
sysctl --system
```

### 2. 安装容器运行时

#### 安装 Docker（推荐）
```bash
# CentOS/RHEL
yum install -y yum-utils
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
yum install -y docker-ce docker-ce-cli containerd.io

# Ubuntu
apt-get update
apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io
```

#### 配置 Docker
```bash
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << EOF
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m"
  },
  "storage-driver": "overlay2"
}
EOF

systemctl enable docker
systemctl start docker
```

### 3. 安装 Kubernetes 组件

#### 添加 K8s 仓库
```bash
# CentOS/RHEL
cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://packages.cloud.google.com/yum/doc/yum-key.gpg https://packages.cloud.google.com/yum/doc/rpm-package-key.gpg
EOF

# Ubuntu
curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -
cat > /etc/apt/sources.list.d/kubernetes.list << EOF
deb https://apt.kubernetes.io/ kubernetes-xenial main
EOF
apt-get update
```

#### 安装 kubelet、kubeadm、kubectl
```bash
# CentOS/RHEL
yum install -y kubelet kubeadm kubectl --disableexcludes=kubernetes

# Ubuntu
apt-get install -y kubelet kubeadm kubectl
apt-mark hold kubelet kubeadm kubectl

# 启用 kubelet
systemctl enable kubelet
```

## 🎯 初始化集群

### 1. 初始化 Master 节点

在 **Master 节点** 上执行：

```bash
# 初始化集群
kubeadm init \
  --apiserver-advertise-address=************ \
  --pod-network-cidr=**********/16 \
  --service-cidr=*********/12

# 配置 kubectl
mkdir -p $HOME/.kube
cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
chown $(id -u):$(id -g) $HOME/.kube/config
```

**重要**：保存初始化输出中的 join 命令，类似：
```bash
kubeadm join ************:6443 --token xxx \
    --discovery-token-ca-cert-hash sha256:xxx
```

### 2. 安装网络插件

在 **Master 节点** 上安装 Flannel：

```bash
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
```

### 3. 加入 Worker 节点

在 **Worker 节点** 上执行之前保存的 join 命令：

```bash
kubeadm join ************:6443 --token xxx \
    --discovery-token-ca-cert-hash sha256:xxx
```

### 4. 验证集群

在 **Master 节点** 上验证：

```bash
# 查看节点状态
kubectl get nodes

# 查看系统 Pod
kubectl get pods -n kube-system

# 预期输出类似：
# NAME         STATUS   ROLES    AGE   VERSION
# k8s-master   Ready    master   5m    v1.21.0
# k8s-worker   Ready    <none>   2m    v1.21.0
```

## 🔧 集群优化配置

### 1. 允许 Master 节点调度 Pod（可选）

如果资源有限，可以让 Master 节点也运行应用 Pod：

```bash
kubectl taint nodes --all node-role.kubernetes.io/master-
```

### 2. 安装存储类

#### 本地存储类
```bash
cat > local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF

kubectl apply -f local-storage-class.yaml
```

### 3. 安装 Metrics Server（可选）

```bash
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
```

## 🚨 故障排除

### 常见问题

1. **节点 NotReady**
   ```bash
   # 检查 kubelet 日志
   journalctl -xeu kubelet
   
   # 检查网络插件
   kubectl get pods -n kube-flannel
   ```

2. **Pod 无法启动**
   ```bash
   # 检查 Pod 状态
   kubectl describe pod <pod-name>
   
   # 检查节点资源
   kubectl top nodes
   ```

3. **网络问题**
   ```bash
   # 检查防火墙
   systemctl status firewalld
   
   # 检查路由
   ip route show
   ```

## ✅ 验证清单

- [ ] 两个节点都显示 Ready 状态
- [ ] 所有系统 Pod 都在 Running 状态
- [ ] 可以创建和删除测试 Pod
- [ ] Pod 之间可以互相通信
- [ ] 存储类配置正确

## 🎉 下一步

集群搭建完成后，您就可以使用我们提供的 Dify 部署方案了：

```bash
cd k8s-deployment
./deploy.sh  # Linux
# 或
deploy.bat   # Windows
```

## 📚 参考资源

- [Kubernetes 官方文档](https://kubernetes.io/docs/)
- [kubeadm 安装指南](https://kubernetes.io/docs/setup/production-environment/tools/kubeadm/)
- [网络插件对比](https://kubernetes.io/docs/concepts/cluster-administration/networking/)

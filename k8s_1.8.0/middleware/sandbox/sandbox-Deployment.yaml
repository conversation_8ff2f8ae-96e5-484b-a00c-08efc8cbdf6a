apiVersion: apps/v1
kind: Deployment
metadata:
  name: sandbox
  namespace: dify
  labels:
    app: sandbox
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: sandbox
  template:
    metadata:
      labels:
        app: sandbox
    spec:
      automountServiceAccountToken: false
      containers:
      - name: sandbox
        image: langgenius/dify-sandbox:0.2.12
        imagePullPolicy: IfNotPresent
        env:
          - name: API_KEY
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: SANDBOX_API_KEY
          - name: GIN_MODE
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: SANDBOX_GIN_MODE
          - name: WORKER_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: SANDBOX_WORKER_TIMEOUT
          - name: ENABLE_NETWORK
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: SANDBOX_ENABLE_NETWORK
          - name: HTTP_PROXY
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: SANDBOX_HTTP_PROXY
          - name: HTTPS_PROXY
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: SANDBOX_HTTPS_PROXY
          - name: SANDBOX_PORT
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: SANDBOX_PORT
          - name: PIP_MIRROR_URL
            valueFrom:
              configMapKeyRef:
                name: dify-config
                key: PIP_MIRROR_URL
        livenessProbe:
          exec:
            command:
              - "curl"
              - "-f"
              - "http://localhost:8194/health"
        ports:
          - containerPort: 8194
        resources:
          limits:
            cpu: 200m
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 200Mi
        imagePullPolicy: IfNotPresent
        volumeMounts:
          - mountPath: /dependencies
            name: sandbox-cm0
          - mountPath: /conf
            name: sandbox-cm1
      volumes:
        - configMap:
            name: sandbox-cm0
          name: sandbox-cm0
        - configMap:
            name: sandbox-cm1
          name: sandbox-cm1
apiVersion: v1
data:
  config.yaml: |
    app:
      port: 8194
      debug: True
      key: sandbox
    max_workers: 4
    max_requests: 50
    worker_timeout: 5
    python_path: /usr/local/bin/python3
    enable_network: True # please make sure there is no network risk in your environment
    allowed_syscalls: # please leave it empty if you have no idea how seccomp works
    proxy:
      socks5: ''
      http: ''
      https: ''
  config.yaml.example: |
    app:
      port: 8194
      debug: True
      key: sandbox
    max_workers: 4
    max_requests: 50
    worker_timeout: 5
    python_path: /usr/local/bin/python3
    python_lib_path:
      - /usr/local/lib/python3.10
      - /usr/lib/python3.10
      - /usr/lib/python3
      - /usr/lib/x86_64-linux-gnu
      - /etc/ssl/certs/ca-certificates.crt
      - /etc/nsswitch.conf
      - /etc/hosts
      - /etc/resolv.conf
      - /run/systemd/resolve/stub-resolv.conf
      - /run/resolvconf/resolv.conf
      - /etc/localtime
      - /usr/share/zoneinfo
      - /etc/timezone
      # add more paths if needed
    python_pip_mirror_url: https://pypi.tuna.tsinghua.edu.cn/simple
    nodejs_path: /usr/local/bin/node
    enable_network: True
    allowed_syscalls:
      - 1
      - 2
      - 3
      # add all the syscalls which you require
    proxy:
      socks5: ''
      http: ''
      https: ''
kind: ConfigMap
metadata:
  labels:
    io.kompose.service: sandbox
  name: sandbox-cm1
  namespace: dify
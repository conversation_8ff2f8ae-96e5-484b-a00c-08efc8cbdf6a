apiVersion: v1
data:
  proxy.conf.template: |
    # Please do not directly edit this file. Instead, modify the .env variables related to NGINX configuration.

    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Port $server_port;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_buffering off;
    proxy_read_timeout ${NGINX_PROXY_READ_TIMEOUT};
    proxy_send_timeout ${NGINX_PROXY_SEND_TIMEOUT};
kind: ConfigMap
metadata:
  annotations:
    use-subpath: "true"
  labels:
    io.kompose.service: nginx
  name: nginx-cm1
  namespace: dify
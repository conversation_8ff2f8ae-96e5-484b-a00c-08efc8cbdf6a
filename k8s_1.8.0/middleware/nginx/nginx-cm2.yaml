apiVersion: v1
data:
  https.conf.template: |-
    # Please do not directly edit this file. Instead, modify the .env variables related to NGINX configuration.

    listen ${NGINX_SSL_PORT} ssl;
    ssl_certificate ${SSL_CERTIFICATE_PATH};
    ssl_certificate_key ${SSL_CERTIFICATE_KEY_PATH};
    ssl_protocols ${NGINX_SSL_PROTOCOLS};
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
kind: ConfigMap
metadata:
  annotations:
    use-subpath: "true"
  labels:
    io.kompose.service: nginx
  name: nginx-cm2
  namespace: dify
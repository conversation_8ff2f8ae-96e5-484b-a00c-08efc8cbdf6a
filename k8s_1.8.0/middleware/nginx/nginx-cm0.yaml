apiVersion: v1
data:
  nginx.conf.template: |-
    # Please do not directly edit this file. Instead, modify the .env variables related to NGINX configuration.

    user  nginx;
    worker_processes  ${NGINX_WORKER_PROCESSES};

    error_log  /var/log/nginx/error.log notice;
    pid        /var/run/nginx.pid;


    events {
        worker_connections  1024;
    }


    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;

        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for"';

        access_log  /var/log/nginx/access.log  main;

        sendfile        on;
        #tcp_nopush     on;

        keepalive_timeout  ${NGINX_KEEPALIVE_TIMEOUT};

        #gzip  on;
        client_max_body_size ${NGINX_CLIENT_MAX_BODY_SIZE};

        include /etc/nginx/conf.d/*.conf;
    }
kind: ConfigMap
metadata:
  annotations:
    use-subpath: "true"
  labels:
    io.kompose.service: nginx
  name: nginx-cm0
  namespace: dify
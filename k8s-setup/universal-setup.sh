#!/bin/bash

# Kubernetes 集群通用搭建脚本（兼容多种 shell）
# 支持任意数量节点的集群搭建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 显示欢迎信息
show_welcome() {
    cat << 'EOF'
========================================
    Kubernetes 集群通用搭建工具
========================================

本工具支持搭建任意规模的 Kubernetes 集群：
✓ 单节点集群（Master 兼 Worker）
✓ 多节点集群（1 Master + N Workers）

支持的操作系统：
✓ CentOS 7.9+ / RHEL 7+
✓ Ubuntu 18.04+ / Debian 10+

前置要求：
✓ 每台服务器至少 4GB 内存，2 CPU 核心
✓ 服务器之间网络互通
✓ 以 root 用户运行此脚本

EOF
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."

    # 检查是否为 root
    if [ "$(id -u)" -ne 0 ]; then
        log_error "请以 root 用户运行此脚本"
        exit 1
    fi

    # 检查操作系统
    if [ -f /etc/redhat-release ]; then
        OS="centos"
        log_info "检测到 CentOS/RHEL 系统"
    elif [ -f /etc/lsb-release ]; then
        OS="ubuntu"
        log_info "检测到 Ubuntu 系统"
    elif [ -f /etc/debian_version ]; then
        OS="ubuntu"
        log_info "检测到 Debian 系统"
    else
        log_error "不支持的操作系统"
        exit 1
    fi

    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        log_warning "无法连接到外网，可能影响软件包下载"
    fi

    log_success "环境检查通过"
}

# 获取配置信息
get_configuration() {
    log_info "收集配置信息..."

    echo "请选择当前节点类型："
    echo "1) Master 节点（控制平面）"
    echo "2) Worker 节点（工作节点）"
    printf "请输入选择 (1-2): "
    read node_type

    case $node_type in
        1)
            NODE_TYPE="master"
            configure_master_node
            ;;
        2)
            NODE_TYPE="worker"
            configure_worker_node
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 配置 Master 节点
configure_master_node() {
    # 获取本机 IP
    local_ip=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "************")
    log_info "检测到本机 IP: $local_ip"

    printf "请确认 Master 节点 IP [$local_ip]: "
    read master_input
    if [ -z "$master_input" ]; then
        MASTER_IP=$local_ip
    else
        MASTER_IP=$master_input
    fi

    HOSTNAME="k8s-master"

    echo
    printf "是否为单节点集群（Master 兼 Worker）？(y/N): "
    read single_node_reply
    if [ "$single_node_reply" = "y" ] || [ "$single_node_reply" = "Y" ]; then
        SINGLE_NODE="true"
        log_info "配置为单节点集群"
    else
        SINGLE_NODE="false"
        log_info "配置为多节点集群"
    fi

    # 确认配置
    echo
    log_info "Master 节点配置确认："
    echo "  节点类型: Master"
    echo "  主机名: $HOSTNAME"
    echo "  Master IP: $MASTER_IP"
    if [ "$SINGLE_NODE" = "true" ]; then
        echo "  集群类型: 单节点"
    else
        echo "  集群类型: 多节点"
    fi
    echo

    printf "配置正确吗？(y/N): "
    read confirm_reply
    if [ "$confirm_reply" != "y" ] && [ "$confirm_reply" != "Y" ]; then
        log_info "请重新运行脚本"
        exit 0
    fi
}

# 配置 Worker 节点
configure_worker_node() {
    # 获取本机 IP
    local_ip=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "************")
    log_info "检测到本机 IP: $local_ip"

    printf "请输入 Master 节点 IP: "
    read MASTER_IP
    if [ -z "$MASTER_IP" ]; then
        log_error "Master 节点 IP 不能为空"
        exit 1
    fi

    # 生成 Worker 节点主机名
    printf "请输入 Worker 节点编号 [1]: "
    read worker_num
    if [ -z "$worker_num" ]; then
        worker_num=1
    fi
    HOSTNAME="k8s-worker-$worker_num"

    WORKER_IP=$local_ip

    # 确认配置
    echo
    log_info "Worker 节点配置确认："
    echo "  节点类型: Worker"
    echo "  主机名: $HOSTNAME"
    echo "  Worker IP: $WORKER_IP"
    echo "  Master IP: $MASTER_IP"
    echo

    printf "配置正确吗？(y/N): "
    read confirm_reply
    if [ "$confirm_reply" != "y" ] && [ "$confirm_reply" != "Y" ]; then
        log_info "请重新运行脚本"
        exit 0
    fi
}

# 系统基础配置
setup_system() {
    log_info "配置系统基础设置..."

    # 设置主机名
    hostnamectl set-hostname $HOSTNAME

    # 配置 hosts 文件
    setup_hosts_file

    # 关闭防火墙和 SELinux
    if [ "$OS" = "centos" ]; then
        systemctl stop firewalld 2>/dev/null || true
        systemctl disable firewalld 2>/dev/null || true
        setenforce 0 2>/dev/null || true
        sed -i 's/^SELINUX=enforcing$/SELINUX=permissive/' /etc/selinux/config 2>/dev/null || true
    elif [ "$OS" = "ubuntu" ]; then
        ufw disable 2>/dev/null || true
    fi

    # 关闭 swap
    swapoff -a
    sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab

    # 配置内核参数
    cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
EOF

    cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
EOF

    modprobe br_netfilter
    sysctl --system

    log_success "系统基础配置完成"
}

# 配置 hosts 文件
setup_hosts_file() {
    log_info "配置 hosts 文件..."

    # 移除旧的 Kubernetes 集群条目
    sed -i '/# Kubernetes 集群节点/,/^$/d' /etc/hosts

    # 添加新的条目
    cat >> /etc/hosts << EOF

# Kubernetes 集群节点
$MASTER_IP k8s-master
EOF

    # 如果是 Worker 节点，添加自己的条目
    if [ "$NODE_TYPE" = "worker" ]; then
        echo "$WORKER_IP $HOSTNAME" >> /etc/hosts
    fi

    log_success "hosts 文件配置完成"
}

# 安装 Docker
install_docker() {
    log_info "安装 Docker..."

    if command -v docker >/dev/null 2>&1; then
        log_warning "Docker 已安装，跳过安装"
        return
    fi

    if [ "$OS" = "centos" ]; then
        yum install -y yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        yum install -y docker-ce docker-ce-cli containerd.io
    elif [ "$OS" = "ubuntu" ]; then
        apt-get update
        apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
        apt-get update
        apt-get install -y docker-ce docker-ce-cli containerd.io
    fi

    # 配置 Docker
    mkdir -p /etc/docker
    cat > /etc/docker/daemon.json << EOF
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m"
  },
  "storage-driver": "overlay2"
}
EOF

    systemctl enable docker
    systemctl start docker

    log_success "Docker 安装配置完成"
}

# 安装 Kubernetes
install_kubernetes() {
    log_info "安装 Kubernetes 组件..."

    if [ "$OS" = "centos" ]; then
        cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://packages.cloud.google.com/yum/doc/yum-key.gpg https://packages.cloud.google.com/yum/doc/rpm-package-key.gpg
EOF
        yum install -y kubelet kubeadm kubectl --disableexcludes=kubernetes
    elif [ "$OS" = "ubuntu" ]; then
        curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -
        cat > /etc/apt/sources.list.d/kubernetes.list << EOF
deb https://apt.kubernetes.io/ kubernetes-xenial main
EOF
        apt-get update
        apt-get install -y kubelet kubeadm kubectl
        apt-mark hold kubelet kubeadm kubectl
    fi

    systemctl enable kubelet

    log_success "Kubernetes 组件安装完成"
}

# 初始化 Master 节点
init_master() {
    if [ "$NODE_TYPE" != "master" ]; then
        return
    fi

    log_info "初始化 Master 节点..."

    # 初始化集群
    kubeadm init \
        --apiserver-advertise-address=$MASTER_IP \
        --pod-network-cidr=**********/16 \
        --service-cidr=*********/12

    # 配置 kubectl
    mkdir -p $HOME/.kube
    cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
    chown $(id -u):$(id -g) $HOME/.kube/config

    # 安装网络插件
    kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml

    # 如果是单节点集群或用户选择，允许 Master 调度
    if [ "$SINGLE_NODE" = "true" ]; then
        log_info "配置单节点集群，允许 Master 运行应用 Pod..."
        kubectl taint nodes --all node-role.kubernetes.io/master- 2>/dev/null || true
        kubectl taint nodes --all node-role.kubernetes.io/control-plane- 2>/dev/null || true
    else
        echo
        printf "是否允许 Master 节点运行应用 Pod？(y/N): "
        read allow_master_reply
        if [ "$allow_master_reply" = "y" ] || [ "$allow_master_reply" = "Y" ]; then
            log_info "配置 Master 节点允许调度..."
            kubectl taint nodes --all node-role.kubernetes.io/master- 2>/dev/null || true
            kubectl taint nodes --all node-role.kubernetes.io/control-plane- 2>/dev/null || true
        fi
    fi

    # 配置存储类
    cat > local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF
    kubectl apply -f local-storage-class.yaml

    # 生成 join 命令
    JOIN_COMMAND=$(kubeadm token create --print-join-command)

    log_success "Master 节点初始化完成"
}

# 显示结果
show_results() {
    echo
    echo "========================================"
    echo "           安装完成"
    echo "========================================"
    echo

    if [ "$NODE_TYPE" = "master" ]; then
        log_success "Master 节点配置完成！"
        echo

        if [ "$SINGLE_NODE" = "true" ]; then
            echo "单节点集群状态："
            kubectl get nodes
            echo
            log_success "单节点 Kubernetes 集群已就绪！"
            echo "可以直接部署应用，无需添加 Worker 节点。"
        else
            echo "集群状态："
            kubectl get nodes
            echo
            echo "Worker 节点加入命令："
            echo "$JOIN_COMMAND"
            echo
            echo "请在每个 Worker 节点上运行上述命令来加入集群"

            # 保存 join 命令到文件
            echo "$JOIN_COMMAND" > join-command.txt
            log_info "join 命令已保存到 join-command.txt"

            # 生成 Worker 节点配置脚本
            generate_worker_setup_script
        fi

    else
        log_success "Worker 节点基础配置完成！"
        echo
        echo "请从 Master 节点获取 join 命令并运行："
        echo "kubeadm join <master-ip>:6443 --token <token> --discovery-token-ca-cert-hash <hash>"
        echo
        echo "或者运行 Master 节点生成的 worker-setup.sh 脚本"
    fi

    echo
    echo "下一步："
    if [ "$SINGLE_NODE" = "true" ]; then
        echo "1. 验证集群状态: kubectl get nodes"
        echo "2. 部署 Dify: cd ../k8s-deployment && ./deploy.sh"
    else
        echo "1. 在所有 Worker 节点上运行 join 命令"
        echo "2. 验证集群状态: kubectl get nodes"
        echo "3. 部署 Dify: cd ../k8s-deployment && ./deploy.sh"
    fi
}

# 生成 Worker 节点设置脚本
generate_worker_setup_script() {
    log_info "生成 Worker 节点设置脚本..."

    cat > worker-setup.sh << EOF
#!/bin/bash

# Worker 节点快速设置脚本
# 在 Worker 节点上以 root 权限运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "\${BLUE}[INFO]\${NC} %s\n" "\$1"
}

log_success() {
    printf "\${GREEN}[SUCCESS]\${NC} %s\n" "\$1"
}

log_error() {
    printf "\${RED}[ERROR]\${NC} %s\n" "\$1"
}

echo "========================================"
echo "    Worker 节点快速设置"
echo "========================================"
echo

# 获取 Worker 节点编号
printf "请输入 Worker 节点编号 [1]: "
read worker_num
if [ -z "\$worker_num" ]; then
    worker_num=1
fi

log_info "配置 Worker 节点 \$worker_num..."

# 设置主机名
hostnamectl set-hostname k8s-worker-\$worker_num

# 配置 hosts
cat >> /etc/hosts << 'HOSTS_EOF'

# Kubernetes 集群节点
$MASTER_IP k8s-master
HOSTS_EOF

log_info "基础配置完成，现在加入集群..."

# 加入集群
$JOIN_COMMAND

if [ \$? -eq 0 ]; then
    log_success "Worker 节点 \$worker_num 成功加入集群！"
    echo "请在 Master 节点上运行 'kubectl get nodes' 验证"
else
    log_error "加入集群失败，请检查网络连接和防火墙设置"
    exit 1
fi
EOF

    chmod +x worker-setup.sh
    log_success "Worker 节点设置脚本已生成: worker-setup.sh"
    echo "请将此脚本复制到 Worker 节点并运行"
}

# 主函数
main() {
    show_welcome

    printf "按 Enter 继续，或 Ctrl+C 退出..."
    read dummy
    echo

    check_environment
    get_configuration
    setup_system
    install_docker
    install_kubernetes
    init_master
    show_results
}

# 如果直接运行脚本
if [ "${0##*/}" = "universal-setup.sh" ]; then
    main "$@"
fi

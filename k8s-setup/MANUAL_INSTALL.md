# Kubernetes 手动安装指南（内网环境）

当自动脚本遇到网络问题时，可以按照此指南手动安装。

## 🔧 解决 GPG 密钥问题

### 方法1：跳过 GPG 验证（推荐）

#### Ubuntu/Debian 系统：
```bash
# 1. 添加仓库（跳过 GPG 验证）
cat > /etc/apt/sources.list.d/kubernetes.list << EOF
deb [trusted=yes] https://apt.kubernetes.io/ kubernetes-xenial main
EOF

# 2. 更新并安装
apt-get update
apt-get install -y kubelet kubeadm kubectl --allow-unauthenticated
apt-mark hold kubelet kubeadm kubectl
```

#### CentOS/RHEL 系统：
```bash
# 1. 添加仓库（跳过 GPG 验证）
cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=0
repo_gpgcheck=0
EOF

# 2. 安装
yum install -y kubelet kubeadm kubectl --disableexcludes=kubernetes
```

### 方法2：使用本地软件包

#### Ubuntu/Debian：
```bash
# 安装 Docker
apt-get update
apt-get install -y docker.io

# 尝试从 snap 安装 Kubernetes
snap install kubelet --classic
snap install kubeadm --classic  
snap install kubectl --classic
```

#### CentOS/RHEL：
```bash
# 安装 Docker
yum install -y docker

# 如果有 EPEL 仓库
yum install -y epel-release
yum install -y kubelet kubeadm kubectl
```

## 🚀 完整手动安装步骤

### 步骤1：系统准备（所有节点）

```bash
# 1. 设置主机名
# Master 节点：
hostnamectl set-hostname k8s-master

# Worker 节点：
hostnamectl set-hostname k8s-worker-1  # 根据节点编号调整

# 2. 配置 hosts 文件
cat >> /etc/hosts << EOF

# Kubernetes 集群节点
************ k8s-master
************ k8s-worker-1
************ k8s-worker-2
EOF

# 3. 关闭防火墙和 SELinux
# CentOS/RHEL:
systemctl stop firewalld
systemctl disable firewalld
setenforce 0
sed -i 's/^SELINUX=enforcing$/SELINUX=permissive/' /etc/selinux/config

# Ubuntu/Debian:
ufw disable

# 4. 关闭 swap
swapoff -a
sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab

# 5. 配置内核参数
cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
EOF

cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
EOF

modprobe br_netfilter
sysctl --system
```

### 步骤2：安装 Docker（所有节点）

```bash
# Ubuntu/Debian:
apt-get update
apt-get install -y docker.io

# CentOS/RHEL:
yum install -y docker

# 配置 Docker
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << EOF
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m"
  },
  "storage-driver": "overlay2"
}
EOF

systemctl enable docker
systemctl start docker
```

### 步骤3：安装 Kubernetes 组件（所有节点）

选择以下方法之一：

#### 方法A：使用官方仓库（跳过验证）
```bash
# Ubuntu/Debian:
cat > /etc/apt/sources.list.d/kubernetes.list << EOF
deb [trusted=yes] https://apt.kubernetes.io/ kubernetes-xenial main
EOF
apt-get update
apt-get install -y kubelet kubeadm kubectl --allow-unauthenticated
apt-mark hold kubelet kubeadm kubectl

# CentOS/RHEL:
cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=0
repo_gpgcheck=0
EOF
yum install -y kubelet kubeadm kubectl --disableexcludes=kubernetes
```

#### 方法B：使用 snap（Ubuntu）
```bash
snap install kubelet --classic
snap install kubeadm --classic
snap install kubectl --classic
```

#### 方法C：从本地包安装
```bash
# 如果有预下载的 RPM/DEB 包
# Ubuntu:
dpkg -i kubelet_*.deb kubeadm_*.deb kubectl_*.deb

# CentOS:
rpm -ivh kubelet-*.rpm kubeadm-*.rpm kubectl-*.rpm
```

```bash
# 启用 kubelet
systemctl enable kubelet
```

### 步骤4：初始化 Master 节点

```bash
# 在 Master 节点上执行
kubeadm init \
  --apiserver-advertise-address=************ \
  --pod-network-cidr=**********/16 \
  --service-cidr=*********/12 \
  --ignore-preflight-errors=all

# 配置 kubectl
mkdir -p $HOME/.kube
cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
chown $(id -u):$(id -g) $HOME/.kube/config

# 允许 Master 调度（单节点集群）
kubectl taint nodes --all node-role.kubernetes.io/master- 2>/dev/null || true
kubectl taint nodes --all node-role.kubernetes.io/control-plane- 2>/dev/null || true
```

### 步骤5：安装网络插件

#### 方法A：在线安装 Flannel
```bash
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
```

#### 方法B：离线安装（如果网络不通）
```bash
# 创建简化的网络配置
cat > simple-network.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: kube-proxy
  namespace: kube-system
data:
  config.conf: |
    apiVersion: kubeproxy.config.k8s.io/v1alpha1
    kind: KubeProxyConfiguration
    mode: "iptables"
    clusterCIDR: "**********/16"
EOF

kubectl apply -f simple-network.yaml
```

### 步骤6：配置存储类

```bash
cat > local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF

kubectl apply -f local-storage-class.yaml
```

### 步骤7：添加 Worker 节点（可选）

```bash
# 在 Master 节点生成 join 命令
kubeadm token create --print-join-command

# 在 Worker 节点执行返回的命令，例如：
kubeadm join ************:6443 --token xxx --discovery-token-ca-cert-hash sha256:xxx
```

### 步骤8：验证集群

```bash
# 查看节点状态
kubectl get nodes

# 查看系统 Pod
kubectl get pods -A

# 查看集群信息
kubectl cluster-info
```

## 🚨 故障排除

### 1. GPG 密钥问题
```bash
# 完全跳过 GPG 验证
echo 'APT::Get::AllowUnauthenticated "true";' > /etc/apt/apt.conf.d/99allow-unauth
```

### 2. 网络插件问题
```bash
# 检查网络插件状态
kubectl get pods -n kube-flannel

# 如果 Flannel 失败，尝试简单的 host-local 网络
kubectl delete -f flannel.yaml
# 然后使用 host-local 或其他网络插件
```

### 3. 镜像拉取问题
```bash
# 预拉取必要镜像
kubeadm config images pull --image-repository registry.aliyuncs.com/google_containers
```

### 4. 节点 NotReady
```bash
# 检查 kubelet 日志
journalctl -xeu kubelet

# 重启 kubelet
systemctl restart kubelet
```

## ✅ 验证清单

- [ ] 所有节点主机名和 hosts 配置正确
- [ ] Docker 服务正常运行
- [ ] kubelet、kubeadm、kubectl 安装成功
- [ ] Master 节点初始化成功
- [ ] kubectl 可以正常使用
- [ ] 网络插件安装成功
- [ ] 存储类配置完成
- [ ] 所有节点状态为 Ready

完成以上步骤后，您就可以继续部署 Dify 了！

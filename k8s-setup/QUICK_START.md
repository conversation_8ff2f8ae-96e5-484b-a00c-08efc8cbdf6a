# Kubernetes 集群快速搭建指南

## 🚀 通用脚本搭建（推荐，兼容性最好）

### 步骤 1: 在 Master 节点运行
```bash
# 使用兼容性最好的通用脚本
bash universal-setup.sh
```
选择 "1) Master 节点"，按提示配置：
- 确认 Master IP 地址
- 选择单节点或多节点集群

### 步骤 2: 在 Worker 节点运行（多节点集群）
```bash
# 方法1: 使用通用脚本
bash universal-setup.sh
# 选择 "2) Worker 节点"，输入 Master IP 和节点编号

# 方法2: 使用 Master 生成的脚本
bash worker-setup.sh
# 输入 Worker 节点编号即可
```

## 🔧 原版脚本（如果通用脚本有问题）

### 步骤 1: 在 Master 节点运行
```bash
bash quick-setup.sh
```
选择 "1) Master 节点"，按提示输入网络配置。

### 步骤 2: 在 Worker 节点运行
```bash
bash quick-setup.sh
```
选择 "2) Worker 节点"，然后运行 Master 节点生成的 join 命令。

### 步骤 3: 验证集群
在 Master 节点上运行：
```bash
kubectl get nodes
```

## 🔧 分步搭建（高级用户）

### 步骤 1: 所有节点基础配置
在每个节点上运行：
```bash
chmod +x setup-common.sh
./setup-common.sh
```

### 步骤 2: 初始化 Master 节点
在 Master 节点上运行：
```bash
chmod +x setup-master.sh
./setup-master.sh
```

### 步骤 3: Worker 节点加入集群
将 Master 节点生成的 `join-worker.sh` 复制到 Worker 节点并运行：
```bash
chmod +x join-worker.sh
./join-worker.sh
```

## ✅ 验证集群状态

```bash
# 查看节点状态
kubectl get nodes

# 查看系统 Pod
kubectl get pods -n kube-system

# 查看集群信息
kubectl cluster-info
```

## 🎯 部署 Dify

集群搭建完成后，部署 Dify：
```bash
cd ../k8s-deployment
./deploy.sh  # Linux
# 或
deploy.bat   # Windows
```

## 🚨 常见问题

### 1. 节点 NotReady
```bash
# 检查网络插件
kubectl get pods -n kube-flannel

# 重启 kubelet
systemctl restart kubelet
```

### 2. join 命令过期
在 Master 节点重新生成：
```bash
kubeadm token create --print-join-command
```

### 3. 网络问题
检查防火墙和 SELinux：
```bash
systemctl status firewalld
getenforce
```

## 📋 服务器要求

### 支持的集群规模
- **单节点集群**：1台服务器（Master 兼 Worker）
- **多节点集群**：1台 Master + N台 Worker（N ≥ 1）

### 硬件要求
- **最低配置**：每台4GB内存，2CPU核心，50GB磁盘
- **推荐配置**：每台8GB内存，4CPU核心，100GB磁盘

### 软件要求
- **操作系统**：CentOS 7.9+ / RHEL 7+ / Ubuntu 18.04+ / Debian 10+
- **网络**：内网互通
- **权限**：root 用户权限

## 🔗 相关文档

- [详细搭建指南](README.md)
- [Dify 部署指南](../k8s-deployment/README.md)

# Kubernetes 离线镜像包制作和使用指南

## 🎯 方案概述

由于您的环境无法访问外网镜像仓库，我们使用离线镜像包的方式来解决问题。

## 📦 方案1：使用预制镜像包（推荐）

### 快速解决当前问题

如果您有其他能上网的机器，可以快速制作镜像包：

```bash
# 在有网络的机器上运行
bash export-k8s-images.sh
```

这会生成：
- `k8s-images.tar` - 镜像包文件
- `import-k8s-images.sh` - 导入脚本
- `K8S_IMAGES_README.md` - 使用说明

### 传输到内网环境

将生成的文件传输到您的内网服务器，然后运行：

```bash
# 在内网服务器上运行
bash import-k8s-images.sh
```

## 🛠️ 方案2：手动制作镜像包

如果您无法运行自动脚本，可以手动制作：

### 在有网络的环境中：

```bash
# 1. 拉取 Kubernetes 核心镜像
docker pull registry.aliyuncs.com/google_containers/kube-apiserver:v1.33.4
docker pull registry.aliyuncs.com/google_containers/kube-controller-manager:v1.33.4
docker pull registry.aliyuncs.com/google_containers/kube-scheduler:v1.33.4
docker pull registry.aliyuncs.com/google_containers/kube-proxy:v1.33.4
docker pull registry.aliyuncs.com/google_containers/pause:3.10
docker pull registry.aliyuncs.com/google_containers/etcd:3.5.15-0
docker pull registry.aliyuncs.com/google_containers/coredns:v1.11.3

# 2. 拉取网络插件镜像
docker pull rancher/mirrored-flannelcni-flannel:v0.19.2
docker pull rancher/mirrored-flannelcni-flannel-cni-plugin:v1.1.0

# 3. 导出镜像
docker save \
  registry.aliyuncs.com/google_containers/kube-apiserver:v1.33.4 \
  registry.aliyuncs.com/google_containers/kube-controller-manager:v1.33.4 \
  registry.aliyuncs.com/google_containers/kube-scheduler:v1.33.4 \
  registry.aliyuncs.com/google_containers/kube-proxy:v1.33.4 \
  registry.aliyuncs.com/google_containers/pause:3.10 \
  registry.aliyuncs.com/google_containers/etcd:3.5.15-0 \
  registry.aliyuncs.com/google_containers/coredns:v1.11.3 \
  rancher/mirrored-flannelcni-flannel:v0.19.2 \
  rancher/mirrored-flannelcni-flannel-cni-plugin:v1.1.0 \
  -o k8s-images.tar
```

### 在内网环境中：

```bash
# 1. 导入镜像
docker load -i k8s-images.tar

# 2. 验证镜像
docker images | grep -E "(kube-|coredns|etcd|pause|flannel)"

# 3. 继续集群初始化
kubeadm init \
  --apiserver-advertise-address=<您的IP> \
  --pod-network-cidr=**********/16 \
  --service-cidr=*********/12 \
  --ignore-preflight-errors=all
```

## 🚀 方案3：立即解决当前卡住的问题

如果您现在就想解决卡住的问题：

### 步骤1：中断当前进程
```bash
# 按 Ctrl+C 中断 kubeadm init
```

### 步骤2：清理并重置
```bash
# 清理之前的尝试
kubeadm reset -f
systemctl stop kubelet
systemctl stop docker
systemctl start docker
```

### 步骤3：使用最小镜像集
```bash
# 手动拉取最关键的镜像（如果网络偶尔可用）
docker pull registry.aliyuncs.com/google_containers/pause:3.10
docker pull registry.aliyuncs.com/google_containers/etcd:3.5.15-0

# 然后跳过镜像检查初始化
kubeadm init \
  --apiserver-advertise-address=<您的IP> \
  --pod-network-cidr=**********/16 \
  --service-cidr=*********/12 \
  --ignore-preflight-errors=all \
  --skip-phases=preflight/images
```

## 📋 镜像清单

### 必需镜像（Kubernetes v1.33.4）
- `kube-apiserver:v1.33.4`
- `kube-controller-manager:v1.33.4`
- `kube-scheduler:v1.33.4`
- `kube-proxy:v1.33.4`
- `pause:3.10`
- `etcd:3.5.15-0`
- `coredns:v1.11.3`

### 网络插件镜像
- `flannel:v0.19.2`
- `flannel-cni-plugin:v1.1.0`

### 可选镜像
- `metrics-server:v0.6.4`
- `local-volume-provisioner:v2.5.0`

## 🔍 故障排除

### 镜像导入失败
```bash
# 检查文件完整性
ls -lh k8s-images.tar

# 检查 Docker 空间
docker system df

# 清理 Docker 缓存
docker system prune -f
```

### kubeadm 仍然卡住
```bash
# 完全跳过镜像拉取
kubeadm init \
  --skip-phases=preflight/images \
  --ignore-preflight-errors=all \
  --apiserver-advertise-address=<您的IP> \
  --pod-network-cidr=**********/16
```

## 💡 建议

1. **优先使用方案1**：自动化脚本最可靠
2. **备用方案2**：手动制作适合特殊情况
3. **紧急方案3**：立即解决当前问题

选择最适合您当前情况的方案！

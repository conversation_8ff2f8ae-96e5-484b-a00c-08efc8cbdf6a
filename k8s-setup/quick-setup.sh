#!/bin/bash

# Kubernetes 集群快速搭建脚本
# 一键式搭建两节点 K8s 集群

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    cat << 'EOF'
========================================
    Kubernetes 集群快速搭建工具
========================================

本工具将帮助您快速搭建一个两节点的 Kubernetes 集群：
- 1 个 Master 节点（控制平面 + 工作负载）
- 1 个 Worker 节点（工作负载）

前置要求：
✓ 两台服务器，每台至少 4GB 内存，2 CPU 核心
✓ CentOS 7.9+ 或 Ubuntu 18.04+ 操作系统
✓ 服务器之间网络互通
✓ 以 root 用户运行此脚本

EOF
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查是否为 root
    if [[ $EUID -ne 0 ]]; then
        log_error "请以 root 用户运行此脚本"
        exit 1
    fi
    
    # 检查操作系统
    if [[ -f /etc/redhat-release ]]; then
        OS="centos"
        log_info "检测到 CentOS/RHEL 系统"
    elif [[ -f /etc/lsb-release ]]; then
        OS="ubuntu"
        log_info "检测到 Ubuntu 系统"
    else
        log_error "不支持的操作系统，仅支持 CentOS/RHEL 和 Ubuntu"
        exit 1
    fi
    
    # 检查网络连接
    if ! ping -c 1 ******* &> /dev/null; then
        log_warning "无法连接到外网，可能影响软件包下载"
    fi
    
    log_success "环境检查通过"
}

# 获取配置信息
get_configuration() {
    log_info "收集配置信息..."
    
    echo "请选择当前节点类型："
    echo "1) Master 节点（控制平面）"
    echo "2) Worker 节点（工作节点）"
    read -p "请输入选择 (1-2): " node_type
    
    case $node_type in
        1)
            NODE_TYPE="master"
            HOSTNAME="k8s-master"
            ;;
        2)
            NODE_TYPE="worker"
            HOSTNAME="k8s-worker"
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
    
    # 获取网络配置
    local_ip=$(ip route get ******* | awk '{print $7; exit}')
    log_info "检测到本机 IP: $local_ip"
    
    read -p "请输入 Master 节点 IP: " MASTER_IP
    read -p "请输入 Worker 节点 IP: " WORKER_IP
    
    # 确认配置
    echo
    log_info "配置确认："
    echo "  节点类型: $NODE_TYPE"
    echo "  主机名: $HOSTNAME"
    echo "  Master IP: $MASTER_IP"
    echo "  Worker IP: $WORKER_IP"
    echo
    
    read -p "配置正确吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "请重新运行脚本"
        exit 0
    fi
}

# 系统基础配置
setup_system() {
    log_info "配置系统基础设置..."
    
    # 设置主机名
    hostnamectl set-hostname $HOSTNAME
    
    # 配置 hosts
    cat >> /etc/hosts << EOF

# Kubernetes 集群节点
$MASTER_IP k8s-master
$WORKER_IP k8s-worker
EOF
    
    # 关闭防火墙和 SELinux
    if [[ $OS == "centos" ]]; then
        systemctl stop firewalld 2>/dev/null || true
        systemctl disable firewalld 2>/dev/null || true
        setenforce 0 2>/dev/null || true
        sed -i 's/^SELINUX=enforcing$/SELINUX=permissive/' /etc/selinux/config 2>/dev/null || true
    elif [[ $OS == "ubuntu" ]]; then
        ufw disable 2>/dev/null || true
    fi
    
    # 关闭 swap
    swapoff -a
    sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab
    
    # 配置内核参数
    cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
EOF

    cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
EOF

    modprobe br_netfilter
    sysctl --system
    
    log_success "系统基础配置完成"
}

# 安装 Docker
install_docker() {
    log_info "安装 Docker..."
    
    if command -v docker &> /dev/null; then
        log_warning "Docker 已安装，跳过安装"
        return
    fi
    
    if [[ $OS == "centos" ]]; then
        yum install -y yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        yum install -y docker-ce docker-ce-cli containerd.io
    elif [[ $OS == "ubuntu" ]]; then
        apt-get update
        apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
        apt-get update
        apt-get install -y docker-ce docker-ce-cli containerd.io
    fi
    
    # 配置 Docker
    mkdir -p /etc/docker
    cat > /etc/docker/daemon.json << EOF
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m"
  },
  "storage-driver": "overlay2"
}
EOF

    systemctl enable docker
    systemctl start docker
    
    log_success "Docker 安装配置完成"
}

# 安装 Kubernetes
install_kubernetes() {
    log_info "安装 Kubernetes 组件..."
    
    if [[ $OS == "centos" ]]; then
        cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://packages.cloud.google.com/yum/doc/yum-key.gpg https://packages.cloud.google.com/yum/doc/rpm-package-key.gpg
EOF
        yum install -y kubelet kubeadm kubectl --disableexcludes=kubernetes
    elif [[ $OS == "ubuntu" ]]; then
        curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -
        cat > /etc/apt/sources.list.d/kubernetes.list << EOF
deb https://apt.kubernetes.io/ kubernetes-xenial main
EOF
        apt-get update
        apt-get install -y kubelet kubeadm kubectl
        apt-mark hold kubelet kubeadm kubectl
    fi
    
    systemctl enable kubelet
    
    log_success "Kubernetes 组件安装完成"
}

# 初始化 Master 节点
init_master() {
    if [[ $NODE_TYPE != "master" ]]; then
        return
    fi
    
    log_info "初始化 Master 节点..."
    
    # 初始化集群
    kubeadm init \
        --apiserver-advertise-address=$MASTER_IP \
        --pod-network-cidr=**********/16 \
        --service-cidr=*********/12
    
    # 配置 kubectl
    mkdir -p $HOME/.kube
    cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
    chown $(id -u):$(id -g) $HOME/.kube/config
    
    # 安装网络插件
    kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
    
    # 允许 Master 调度
    kubectl taint nodes --all node-role.kubernetes.io/master- 2>/dev/null || true
    kubectl taint nodes --all node-role.kubernetes.io/control-plane- 2>/dev/null || true
    
    # 配置存储类
    cat > local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF
    kubectl apply -f local-storage-class.yaml
    
    # 生成 join 命令
    JOIN_COMMAND=$(kubeadm token create --print-join-command)
    
    log_success "Master 节点初始化完成"
}

# 显示结果
show_results() {
    echo
    echo "========================================"
    echo "           安装完成"
    echo "========================================"
    echo
    
    if [[ $NODE_TYPE == "master" ]]; then
        log_success "Master 节点配置完成！"
        echo
        echo "集群状态："
        kubectl get nodes
        echo
        echo "Worker 节点加入命令："
        echo "$JOIN_COMMAND"
        echo
        echo "请在 Worker 节点上运行上述命令来加入集群"
        
        # 保存 join 命令到文件
        echo "$JOIN_COMMAND" > join-command.txt
        log_info "join 命令已保存到 join-command.txt"
        
    else
        log_success "Worker 节点基础配置完成！"
        echo
        echo "请从 Master 节点获取 join 命令并运行："
        echo "kubeadm join <master-ip>:6443 --token <token> --discovery-token-ca-cert-hash <hash>"
    fi
    
    echo
    echo "下一步："
    echo "1. 确保所有节点都加入集群"
    echo "2. 验证集群状态: kubectl get nodes"
    echo "3. 部署 Dify: cd ../k8s-deployment && ./deploy.sh"
}

# 主函数
main() {
    show_welcome
    
    read -p "按 Enter 继续，或 Ctrl+C 退出..."
    echo
    
    check_environment
    get_configuration
    setup_system
    install_docker
    install_kubernetes
    init_master
    show_results
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

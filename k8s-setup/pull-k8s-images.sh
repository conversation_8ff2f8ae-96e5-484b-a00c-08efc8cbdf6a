#!/bin/bash

# Kubernetes 镜像拉取脚本（内网环境）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

echo "========================================"
echo "    Kubernetes 镜像拉取工具"
echo "========================================"
echo

# 检查是否为 root
if [ "$(id -u)" -ne 0 ]; then
    log_error "请以 root 用户运行此脚本"
    exit 1
fi

# 确定 kubeadm 命令路径
if command -v /snap/bin/kubeadm >/dev/null 2>&1; then
    KUBEADM_CMD="/snap/bin/kubeadm"
    log_info "使用 snap 安装的 kubeadm"
elif command -v kubeadm >/dev/null 2>&1; then
    KUBEADM_CMD="kubeadm"
    log_info "使用系统安装的 kubeadm"
else
    log_error "未找到 kubeadm 命令"
    exit 1
fi

# 镜像源列表（按优先级排序）
REGISTRIES=(
    "registry.aliyuncs.com/google_containers"
    "registry.cn-hangzhou.aliyuncs.com/google_containers"
    "k8s.gcr.io"
    "registry.k8s.io"
)

# 尝试不同的镜像源
pull_images() {
    for registry in "${REGISTRIES[@]}"; do
        log_info "尝试从 $registry 拉取镜像..."
        
        if $KUBEADM_CMD config images pull --image-repository=$registry; then
            log_success "成功从 $registry 拉取镜像"
            return 0
        else
            log_warning "从 $registry 拉取镜像失败"
        fi
    done
    
    log_error "所有镜像源都无法访问"
    return 1
}

# 手动拉取关键镜像
manual_pull() {
    log_info "尝试手动拉取关键镜像..."
    
    # 获取 Kubernetes 版本
    K8S_VERSION=$($KUBEADM_CMD version -o short | cut -d'v' -f2)
    log_info "Kubernetes 版本: v$K8S_VERSION"
    
    # 关键镜像列表
    IMAGES=(
        "registry.aliyuncs.com/google_containers/kube-apiserver:v$K8S_VERSION"
        "registry.aliyuncs.com/google_containers/kube-controller-manager:v$K8S_VERSION"
        "registry.aliyuncs.com/google_containers/kube-scheduler:v$K8S_VERSION"
        "registry.aliyuncs.com/google_containers/kube-proxy:v$K8S_VERSION"
        "registry.aliyuncs.com/google_containers/pause:3.10"
        "registry.aliyuncs.com/google_containers/etcd:3.5.15-0"
        "registry.aliyuncs.com/google_containers/coredns:v1.11.3"
    )
    
    local success_count=0
    local total_count=${#IMAGES[@]}
    
    for image in "${IMAGES[@]}"; do
        log_info "拉取镜像: $image"
        if docker pull "$image" 2>/dev/null; then
            log_success "成功拉取: $image"
            success_count=$((success_count + 1))
        else
            log_warning "拉取失败: $image"
        fi
    done
    
    log_info "镜像拉取完成: $success_count/$total_count"
    
    if [ $success_count -gt 0 ]; then
        return 0
    else
        return 1
    fi
}

# 显示当前镜像
show_images() {
    log_info "当前 Docker 镜像："
    docker images | grep -E "(kube-|coredns|etcd|pause)" || log_warning "未找到 Kubernetes 相关镜像"
}

# 主函数
main() {
    log_info "开始拉取 Kubernetes 镜像..."
    
    # 检查 Docker 是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker 服务"
        exit 1
    fi
    
    # 尝试使用 kubeadm 拉取镜像
    if pull_images; then
        log_success "镜像拉取成功！"
    else
        log_warning "kubeadm 拉取失败，尝试手动拉取..."
        if manual_pull; then
            log_success "手动拉取部分镜像成功！"
        else
            log_error "所有拉取方式都失败了"
            echo
            log_info "建议："
            echo "1. 检查网络连接"
            echo "2. 配置 Docker 代理"
            echo "3. 使用离线镜像包"
            echo "4. 跳过镜像拉取步骤：kubeadm init --skip-phases=preflight/images"
            exit 1
        fi
    fi
    
    echo
    show_images
    
    echo
    log_success "镜像准备完成！现在可以运行 kubeadm init"
    echo
    echo "建议的初始化命令："
    echo "$KUBEADM_CMD init \\"
    echo "  --apiserver-advertise-address=<您的IP> \\"
    echo "  --pod-network-cidr=**********/16 \\"
    echo "  --service-cidr=*********/12 \\"
    echo "  --image-repository=registry.aliyuncs.com/google_containers \\"
    echo "  --ignore-preflight-errors=all"
}

# 如果直接运行脚本
if [ "${0##*/}" = "pull-k8s-images.sh" ]; then
    main "$@"
fi

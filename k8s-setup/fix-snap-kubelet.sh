#!/bin/bash

# 修复 snap 安装的 kubelet 服务问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

echo "========================================"
echo "    修复 snap kubelet 服务"
echo "========================================"
echo

# 检查是否为 root
if [ "$(id -u)" -ne 0 ]; then
    log_error "请以 root 用户运行此脚本"
    exit 1
fi

# 检查 snap 是否安装了 kubelet
if ! command -v /snap/bin/kubelet >/dev/null 2>&1; then
    log_error "未找到 snap 安装的 kubelet"
    exit 1
fi

log_info "检测到 snap 安装的 kubelet，开始修复..."

# 1. 创建必要的目录
log_info "创建必要的目录..."
mkdir -p /var/lib/kubelet
mkdir -p /etc/kubernetes
mkdir -p /etc/cni/net.d
mkdir -p /opt/cni/bin

# 2. 创建 kubelet systemd 服务文件
log_info "创建 kubelet systemd 服务文件..."
cat > /etc/systemd/system/kubelet.service << 'EOF'
[Unit]
Description=kubelet: The Kubernetes Node Agent
Documentation=https://kubernetes.io/docs/home/
Wants=network-online.target
After=network-online.target

[Service]
ExecStart=/snap/bin/kubelet
Restart=always
StartLimitInterval=0
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 3. 创建 kubelet 服务目录
mkdir -p /etc/systemd/system/kubelet.service.d

# 4. 创建 kubeadm 配置文件
cat > /etc/systemd/system/kubelet.service.d/10-kubeadm.conf << 'EOF'
# Note: This dropin only works with kubeadm and kubelet v1.11+
[Service]
Environment="KUBELET_KUBECONFIG_ARGS=--bootstrap-kubeconfig=/etc/kubernetes/bootstrap-kubelet.conf --kubeconfig=/etc/kubernetes/kubelet.conf"
Environment="KUBELET_CONFIG_ARGS=--config=/var/lib/kubelet/config.yaml"
# This is a file that "kubeadm init" and "kubeadm join" generates at runtime, populating the KUBELET_KUBEADM_ARGS variable dynamically
EnvironmentFile=-/var/lib/kubelet/kubeadm-flags.env
# This is a file that the user can use for overrides of the kubelet args as a last resort. Preferably, the user should use
# the .NodeRegistration.KubeletExtraArgs object in the configuration files instead. KUBELET_EXTRA_ARGS should be sourced from this file.
EnvironmentFile=-/etc/default/kubelet
ExecStart=
ExecStart=/snap/bin/kubelet $KUBELET_KUBECONFIG_ARGS $KUBELET_CONFIG_ARGS $KUBELET_KUBEADM_ARGS $KUBELET_EXTRA_ARGS
EOF

# 5. 创建符号链接
log_info "创建符号链接..."
ln -sf /snap/bin/kubeadm /usr/local/bin/kubeadm 2>/dev/null || true
ln -sf /snap/bin/kubectl /usr/local/bin/kubectl 2>/dev/null || true
ln -sf /snap/bin/kubelet /usr/local/bin/kubelet 2>/dev/null || true

# 6. 更新 PATH
log_info "更新 PATH 环境变量..."
if ! grep -q "/snap/bin" /etc/environment; then
    sed -i 's|PATH="\(.*\)"|PATH="/snap/bin:\1"|' /etc/environment
fi

# 7. 重新加载 systemd
log_info "重新加载 systemd..."
systemctl daemon-reload

# 8. 启用 kubelet 服务
log_info "启用 kubelet 服务..."
systemctl enable kubelet

# 9. 检查服务状态
log_info "检查服务状态..."
if systemctl is-enabled kubelet >/dev/null 2>&1; then
    log_success "kubelet 服务已启用"
else
    log_warning "kubelet 服务启用可能失败"
fi

# 10. 显示版本信息
echo
log_info "Kubernetes 组件版本："
echo "kubelet: $(/snap/bin/kubelet --version)"
echo "kubeadm: $(/snap/bin/kubeadm version --client --short)"
echo "kubectl: $(/snap/bin/kubectl version --client --short)"

echo
log_success "snap kubelet 服务修复完成！"
echo
echo "下一步："
echo "1. 继续运行集群初始化脚本"
echo "2. 或手动运行: kubeadm init --apiserver-advertise-address=<your-ip> --pod-network-cidr=**********/16"
echo
echo "注意："
echo "- 使用完整路径: /snap/bin/kubeadm, /snap/bin/kubectl"
echo "- 或者使用符号链接: kubeadm, kubectl (已创建)"

#!/bin/bash

# Kubernetes 镜像导出脚本
# 在有网络的环境中运行，准备离线镜像包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

echo "========================================"
echo "    Kubernetes 镜像导出工具"
echo "========================================"
echo

# 检查 Docker
if ! command -v docker >/dev/null 2>&1; then
    log_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    log_error "Docker 未运行，请启动 Docker 服务"
    exit 1
fi

# Kubernetes 版本（可根据需要修改）
K8S_VERSION="1.33.4"
ETCD_VERSION="3.5.15-0"
COREDNS_VERSION="v1.11.3"
PAUSE_VERSION="3.10"

log_info "准备导出 Kubernetes v$K8S_VERSION 相关镜像"

# 镜像列表 - 优先使用国内镜像源
CORE_IMAGES=(
    # Kubernetes 核心组件（必需）
    "registry.aliyuncs.com/google_containers/kube-apiserver:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/kube-controller-manager:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/kube-scheduler:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/kube-proxy:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/pause:$PAUSE_VERSION"
    "registry.aliyuncs.com/google_containers/etcd:$ETCD_VERSION"
    "registry.aliyuncs.com/google_containers/coredns:$COREDNS_VERSION"
)

NETWORK_IMAGES=(
    # 网络插件 - 使用阿里云镜像
    "registry.aliyuncs.com/flannel/flannel:v0.19.2"
    "registry.aliyuncs.com/flannel/flannel-cni-plugin:v1.1.0"

    # 备用：使用 Docker Hub 镜像（可能失败）
    "rancher/mirrored-flannelcni-flannel:v0.19.2"
    "rancher/mirrored-flannelcni-flannel-cni-plugin:v1.1.0"
)

OPTIONAL_IMAGES=(
    # 监控组件（可选）
    "registry.aliyuncs.com/google_containers/metrics-server:v0.6.4"

    # 存储组件（可选）
    "registry.aliyuncs.com/google_containers/local-volume-provisioner:v2.5.0"

    # Calico 网络插件（备用，使用阿里云镜像）
    "registry.aliyuncs.com/calico/cni:v3.26.1"
    "registry.aliyuncs.com/calico/node:v3.26.1"
    "registry.aliyuncs.com/calico/kube-controllers:v3.26.1"
)

# 合并所有镜像
IMAGES=("${CORE_IMAGES[@]}" "${NETWORK_IMAGES[@]}" "${OPTIONAL_IMAGES[@]}")

# 拉取镜像（分类处理）
pull_images() {
    log_info "开始拉取镜像..."

    # 拉取核心镜像（必需）
    log_info "=== 拉取 Kubernetes 核心镜像 ==="
    local core_success=0
    for image in "${CORE_IMAGES[@]}"; do
        log_info "拉取核心镜像: $image"
        if docker pull "$image"; then
            log_success "✓ $image"
            core_success=$((core_success + 1))
        else
            log_error "✗ $image"
        fi
    done

    # 拉取网络插件镜像
    log_info "=== 拉取网络插件镜像 ==="
    local network_success=0
    for image in "${NETWORK_IMAGES[@]}"; do
        log_info "拉取网络镜像: $image"
        if timeout 60 docker pull "$image"; then
            log_success "✓ $image"
            network_success=$((network_success + 1))
        else
            log_warning "✗ $image (跳过，使用备用方案)"
        fi
    done

    # 拉取可选镜像
    log_info "=== 拉取可选镜像 ==="
    local optional_success=0
    for image in "${OPTIONAL_IMAGES[@]}"; do
        log_info "拉取可选镜像: $image"
        if timeout 30 docker pull "$image"; then
            log_success "✓ $image"
            optional_success=$((optional_success + 1))
        else
            log_warning "✗ $image (可选镜像，跳过)"
        fi
    done

    # 统计结果
    local total_success=$((core_success + network_success + optional_success))
    local total_count=$((${#CORE_IMAGES[@]} + ${#NETWORK_IMAGES[@]} + ${#OPTIONAL_IMAGES[@]}))

    echo
    log_info "镜像拉取统计："
    echo "  核心镜像: $core_success/${#CORE_IMAGES[@]}"
    echo "  网络镜像: $network_success/${#NETWORK_IMAGES[@]}"
    echo "  可选镜像: $optional_success/${#OPTIONAL_IMAGES[@]}"
    echo "  总计: $total_success/$total_count"

    # 检查核心镜像是否足够
    if [ $core_success -lt 5 ]; then
        log_error "核心镜像拉取不足，至少需要 5 个核心镜像"
        log_info "建议检查网络连接或使用代理"
        exit 1
    fi

    if [ $network_success -eq 0 ]; then
        log_warning "没有成功拉取网络插件镜像，集群可能需要手动配置网络"
    fi

    log_success "镜像拉取完成，可以继续导出"
}

# 导出镜像
export_images() {
    log_info "导出镜像到 k8s-images.tar..."

    # 获取实际存在的镜像
    local existing_images=()
    for image in "${IMAGES[@]}"; do
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${image}$"; then
            existing_images+=("$image")
        fi
    done

    if [ ${#existing_images[@]} -eq 0 ]; then
        log_error "没有找到可导出的镜像"
        exit 1
    fi

    log_info "导出 ${#existing_images[@]} 个镜像..."

    if docker save "${existing_images[@]}" -o k8s-images.tar; then
        local file_size=$(du -h k8s-images.tar | cut -f1)
        log_success "镜像导出完成: k8s-images.tar ($file_size)"
    else
        log_error "镜像导出失败"
        exit 1
    fi
}

# 生成导入脚本
generate_import_script() {
    log_info "生成镜像导入脚本..."

    cat > import-k8s-images.sh << 'EOF'
#!/bin/bash

# Kubernetes 镜像导入脚本
# 在内网环境中运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

echo "========================================"
echo "    Kubernetes 镜像导入工具"
echo "========================================"
echo

# 检查镜像文件
if [ ! -f "k8s-images.tar" ]; then
    log_error "镜像文件 k8s-images.tar 不存在"
    exit 1
fi

# 检查 Docker
if ! command -v docker >/dev/null 2>&1; then
    log_error "Docker 未安装"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    log_error "Docker 未运行，请启动 Docker 服务"
    exit 1
fi

log_info "开始导入 Kubernetes 镜像..."

# 导入镜像
if docker load -i k8s-images.tar; then
    log_success "镜像导入完成"
else
    log_error "镜像导入失败"
    exit 1
fi

# 显示导入的镜像
log_info "已导入的 Kubernetes 镜像："
docker images | grep -E "(kube-|coredns|etcd|pause|flannel|calico)" | head -20

log_success "所有镜像已成功导入！"
echo
echo "现在可以运行 kubeadm init 初始化集群"
EOF

    chmod +x import-k8s-images.sh
    log_success "导入脚本已生成: import-k8s-images.sh"
}

# 生成使用说明
generate_readme() {
    log_info "生成使用说明..."

    cat > K8S_IMAGES_README.md << EOF
# Kubernetes 离线镜像包使用指南

## 文件说明

- \`k8s-images.tar\` - Kubernetes 镜像包
- \`import-k8s-images.sh\` - 镜像导入脚本
- \`K8S_IMAGES_README.md\` - 本说明文件

## 使用步骤

### 1. 传输文件到内网环境
将以下文件传输到内网服务器：
- k8s-images.tar
- import-k8s-images.sh

### 2. 导入镜像
\`\`\`bash
# 在内网服务器上运行
chmod +x import-k8s-images.sh
./import-k8s-images.sh
\`\`\`

### 3. 验证镜像
\`\`\`bash
# 查看导入的镜像
docker images | grep -E "(kube-|coredns|etcd|pause|flannel)"
\`\`\`

### 4. 初始化集群
\`\`\`bash
# 使用本地镜像初始化集群
kubeadm init \\
  --apiserver-advertise-address=<您的IP> \\
  --pod-network-cidr=**********/16 \\
  --service-cidr=*********/12 \\
  --ignore-preflight-errors=all
\`\`\`

## 包含的镜像

### Kubernetes 核心组件
- kube-apiserver:v$K8S_VERSION
- kube-controller-manager:v$K8S_VERSION
- kube-scheduler:v$K8S_VERSION
- kube-proxy:v$K8S_VERSION
- pause:$PAUSE_VERSION
- etcd:$ETCD_VERSION
- coredns:$COREDNS_VERSION

### 网络插件
- Flannel v0.19.2
- Calico v3.26.1 (备用)

### 其他组件
- metrics-server (监控)
- local-volume-provisioner (存储)

## 注意事项

1. 确保目标服务器已安装 Docker
2. 确保有足够的磁盘空间
3. 导入过程可能需要几分钟时间
4. 如果某些镜像导入失败，可以单独处理

## 故障排除

### 导入失败
- 检查 Docker 服务状态
- 检查磁盘空间
- 检查镜像文件完整性

### 镜像缺失
- 检查镜像是否正确导入
- 手动拉取缺失的镜像
- 使用 docker images 查看可用镜像

更多信息请参考 Kubernetes 官方文档。
EOF

    log_success "使用说明已生成: K8S_IMAGES_README.md"
}

# 主函数
main() {
    pull_images
    export_images
    generate_import_script
    generate_readme

    echo
    echo "========================================"
    echo "           导出完成"
    echo "========================================"
    echo
    log_success "Kubernetes 离线镜像包已准备完成！"
    echo
    echo "生成的文件："
    echo "  - k8s-images.tar (镜像包)"
    echo "  - import-k8s-images.sh (导入脚本)"
    echo "  - K8S_IMAGES_README.md (使用说明)"
    echo
    echo "请将这些文件传输到内网环境并按照说明进行导入。"
}

# 如果直接运行脚本
if [ "${0##*/}" = "export-k8s-images.sh" ]; then
    main "$@"
fi

#!/bin/bash

# Kubernetes 集群离线安装脚本
# 适用于无外网连接的内网环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 显示欢迎信息
show_welcome() {
    cat << 'EOF'
========================================
    Kubernetes 集群离线安装工具
========================================

本工具专为内网环境设计，无需外网连接：
✓ 使用本地软件包安装
✓ 跳过 GPG 验证
✓ 支持单节点和多节点集群
✓ 自动配置网络和存储

适用场景：
✓ 完全内网环境
✓ 网络受限环境
✓ 安全要求较高的环境

EOF
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."

    # 检查是否为 root
    if [ "$(id -u)" -ne 0 ]; then
        log_error "请以 root 用户运行此脚本"
        exit 1
    fi

    # 检查操作系统
    if [ -f /etc/redhat-release ]; then
        OS="centos"
        log_info "检测到 CentOS/RHEL 系统"
    elif [ -f /etc/lsb-release ]; then
        OS="ubuntu"
        log_info "检测到 Ubuntu 系统"
    elif [ -f /etc/debian_version ]; then
        OS="ubuntu"
        log_info "检测到 Debian 系统"
    else
        log_error "不支持的操作系统"
        exit 1
    fi

    log_success "环境检查通过"
}

# 获取配置信息
get_configuration() {
    log_info "收集配置信息..."

    echo "请选择当前节点类型："
    echo "1) Master 节点（控制平面）"
    echo "2) Worker 节点（工作节点）"
    printf "请输入选择 (1-2): "
    read node_type

    case $node_type in
        1)
            NODE_TYPE="master"
            configure_master_node
            ;;
        2)
            NODE_TYPE="worker"
            configure_worker_node
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 配置 Master 节点
configure_master_node() {
    # 获取本机 IP
    local_ip=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "************")
    log_info "检测到本机 IP: $local_ip"

    printf "请确认 Master 节点 IP [$local_ip]: "
    read master_input
    if [ -z "$master_input" ]; then
        MASTER_IP=$local_ip
    else
        MASTER_IP=$master_input
    fi

    HOSTNAME="k8s-master"

    echo
    printf "是否为单节点集群（Master 兼 Worker）？(y/N): "
    read single_node_reply
    if [ "$single_node_reply" = "y" ] || [ "$single_node_reply" = "Y" ]; then
        SINGLE_NODE="true"
        log_info "配置为单节点集群"
    else
        SINGLE_NODE="false"
        log_info "配置为多节点集群"
    fi

    # 确认配置
    echo
    log_info "Master 节点配置确认："
    echo "  节点类型: Master"
    echo "  主机名: $HOSTNAME"
    echo "  Master IP: $MASTER_IP"
    if [ "$SINGLE_NODE" = "true" ]; then
        echo "  集群类型: 单节点"
    else
        echo "  集群类型: 多节点"
    fi
    echo

    printf "配置正确吗？(y/N): "
    read confirm_reply
    if [ "$confirm_reply" != "y" ] && [ "$confirm_reply" != "Y" ]; then
        log_info "请重新运行脚本"
        exit 0
    fi
}

# 配置 Worker 节点
configure_worker_node() {
    # 获取本机 IP
    local_ip=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}' || echo "************")
    log_info "检测到本机 IP: $local_ip"

    printf "请输入 Master 节点 IP: "
    read MASTER_IP
    if [ -z "$MASTER_IP" ]; then
        log_error "Master 节点 IP 不能为空"
        exit 1
    fi

    # 生成 Worker 节点主机名
    printf "请输入 Worker 节点编号 [1]: "
    read worker_num
    if [ -z "$worker_num" ]; then
        worker_num=1
    fi
    HOSTNAME="k8s-worker-$worker_num"

    WORKER_IP=$local_ip

    # 确认配置
    echo
    log_info "Worker 节点配置确认："
    echo "  节点类型: Worker"
    echo "  主机名: $HOSTNAME"
    echo "  Worker IP: $WORKER_IP"
    echo "  Master IP: $MASTER_IP"
    echo

    printf "配置正确吗？(y/N): "
    read confirm_reply
    if [ "$confirm_reply" != "y" ] && [ "$confirm_reply" != "Y" ]; then
        log_info "请重新运行脚本"
        exit 0
    fi
}

# 系统基础配置
setup_system() {
    log_info "配置系统基础设置..."

    # 设置主机名
    hostnamectl set-hostname $HOSTNAME

    # 配置 hosts 文件
    setup_hosts_file

    # 关闭防火墙和 SELinux
    if [ "$OS" = "centos" ]; then
        systemctl stop firewalld 2>/dev/null || true
        systemctl disable firewalld 2>/dev/null || true
        setenforce 0 2>/dev/null || true
        sed -i 's/^SELINUX=enforcing$/SELINUX=permissive/' /etc/selinux/config 2>/dev/null || true
    elif [ "$OS" = "ubuntu" ]; then
        ufw disable 2>/dev/null || true
    fi

    # 关闭 swap
    swapoff -a
    sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab

    # 配置内核参数
    cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
EOF

    cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
EOF

    modprobe br_netfilter
    sysctl --system

    log_success "系统基础配置完成"
}

# 配置 hosts 文件
setup_hosts_file() {
    log_info "配置 hosts 文件..."

    # 移除旧的 Kubernetes 集群条目
    sed -i '/# Kubernetes 集群节点/,/^$/d' /etc/hosts

    # 添加新的条目
    cat >> /etc/hosts << EOF

# Kubernetes 集群节点
$MASTER_IP k8s-master
EOF

    # 如果是 Worker 节点，添加自己的条目
    if [ "$NODE_TYPE" = "worker" ]; then
        echo "$WORKER_IP $HOSTNAME" >> /etc/hosts
    fi

    log_success "hosts 文件配置完成"
}

# 安装 Docker（离线方式）
install_docker_offline() {
    log_info "安装 Docker（离线模式）..."

    if command -v docker >/dev/null 2>&1; then
        log_warning "Docker 已安装，跳过安装"
        return
    fi

    if [ "$OS" = "centos" ]; then
        # 尝试从本地仓库安装
        yum install -y docker || {
            log_warning "无法从仓库安装 Docker，尝试安装 docker.io"
            yum install -y docker.io || {
                log_error "Docker 安装失败，请手动安装 Docker"
                exit 1
            }
        }
    elif [ "$OS" = "ubuntu" ]; then
        # 尝试从本地仓库安装
        apt-get update
        apt-get install -y docker.io || {
            log_error "Docker 安装失败，请手动安装 Docker"
            exit 1
        }
    fi

    # 配置 Docker
    mkdir -p /etc/docker
    cat > /etc/docker/daemon.json << EOF
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m"
  },
  "storage-driver": "overlay2"
}
EOF

    systemctl enable docker
    systemctl start docker

    log_success "Docker 安装配置完成"
}

# 安装 Kubernetes（离线方式）
install_kubernetes_offline() {
    log_info "安装 Kubernetes 组件（离线模式）..."

    if [ "$OS" = "centos" ]; then
        # 尝试从 EPEL 或本地仓库安装
        yum install -y kubelet kubeadm kubectl || {
            log_warning "无法从标准仓库安装，尝试替代方案..."
            # 可以在这里添加从本地 RPM 包安装的逻辑
            log_error "Kubernetes 组件安装失败"
            log_info "请手动下载并安装以下 RPM 包："
            echo "  - kubelet"
            echo "  - kubeadm"
            echo "  - kubectl"
            exit 1
        }
    elif [ "$OS" = "ubuntu" ]; then
        # 尝试从 snap 安装（通常在离线环境中可用）
        if command -v snap >/dev/null 2>&1; then
            log_info "尝试使用 snap 安装 Kubernetes 组件..."
            snap install kubelet --classic || true
            snap install kubeadm --classic || true
            snap install kubectl --classic || true
        fi

        # 如果 snap 安装失败，尝试 apt
        if ! command -v kubectl >/dev/null 2>&1; then
            log_info "尝试从本地仓库安装..."
            apt-get update
            apt-get install -y kubelet kubeadm kubectl || {
                log_error "Kubernetes 组件安装失败"
                log_info "请手动安装以下组件："
                echo "  - kubelet"
                echo "  - kubeadm"
                echo "  - kubectl"
                exit 1
            }
            apt-mark hold kubelet kubeadm kubectl
        fi
    fi

    systemctl enable kubelet

    log_success "Kubernetes 组件安装完成"
}

# 初始化 Master 节点
init_master() {
    if [ "$NODE_TYPE" != "master" ]; then
        return
    fi

    log_info "初始化 Master 节点..."

    # 初始化集群
    kubeadm init \
        --apiserver-advertise-address=$MASTER_IP \
        --pod-network-cidr=**********/16 \
        --service-cidr=*********/12 \
        --ignore-preflight-errors=all

    # 配置 kubectl
    mkdir -p $HOME/.kube
    cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
    chown $(id -u):$(id -g) $HOME/.kube/config

    # 安装网络插件（使用本地文件或简化配置）
    install_network_plugin_offline

    # 如果是单节点集群或用户选择，允许 Master 调度
    if [ "$SINGLE_NODE" = "true" ]; then
        log_info "配置单节点集群，允许 Master 运行应用 Pod..."
        kubectl taint nodes --all node-role.kubernetes.io/master- 2>/dev/null || true
        kubectl taint nodes --all node-role.kubernetes.io/control-plane- 2>/dev/null || true
    else
        echo
        printf "是否允许 Master 节点运行应用 Pod？(y/N): "
        read allow_master_reply
        if [ "$allow_master_reply" = "y" ] || [ "$allow_master_reply" = "Y" ]; then
            log_info "配置 Master 节点允许调度..."
            kubectl taint nodes --all node-role.kubernetes.io/master- 2>/dev/null || true
            kubectl taint nodes --all node-role.kubernetes.io/control-plane- 2>/dev/null || true
        fi
    fi

    # 配置存储类
    cat > local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF
    kubectl apply -f local-storage-class.yaml

    # 生成 join 命令
    JOIN_COMMAND=$(kubeadm token create --print-join-command)

    log_success "Master 节点初始化完成"
}

# 安装网络插件（离线模式）
install_network_plugin_offline() {
    log_info "安装网络插件（离线模式）..."

    # 创建简化的 Flannel 配置
    cat > flannel-offline.yaml << EOF
apiVersion: v1
kind: Namespace
metadata:
  name: kube-flannel
  labels:
    pod-security.kubernetes.io/enforce: privileged
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: flannel
  namespace: kube-flannel
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: flannel
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["list", "watch"]
- apiGroups: [""]
  resources: ["nodes/status"]
  verbs: ["patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: flannel
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: flannel
subjects:
- kind: ServiceAccount
  name: flannel
  namespace: kube-flannel
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kube-flannel-cfg
  namespace: kube-flannel
  labels:
    tier: node
    app: flannel
data:
  cni-conf.json: |
    {
      "name": "cbr0",
      "cniVersion": "0.3.1",
      "plugins": [
        {
          "type": "flannel",
          "delegate": {
            "hairpinMode": true,
            "isDefaultGateway": true
          }
        },
        {
          "type": "portmap",
          "capabilities": {
            "portMappings": true
          }
        }
      ]
    }
  net-conf.json: |
    {
      "Network": "**********/16",
      "Backend": {
        "Type": "vxlan"
      }
    }
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: kube-flannel-ds
  namespace: kube-flannel
  labels:
    tier: node
    app: flannel
spec:
  selector:
    matchLabels:
      app: flannel
  template:
    metadata:
      labels:
        tier: node
        app: flannel
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/os
                operator: In
                values:
                - linux
      hostNetwork: true
      priorityClassName: system-node-critical
      tolerations:
      - operator: Exists
        effect: NoSchedule
      serviceAccountName: flannel
      initContainers:
      - name: install-cni-plugin
        image: rancher/mirrored-flannelcni-flannel-cni-plugin:v1.1.0
        command:
        - cp
        args:
        - -f
        - /flannel
        - /opt/cni/bin/flannel
        volumeMounts:
        - name: cni-plugin
          mountPath: /opt/cni/bin
      - name: install-cni
        image: rancher/mirrored-flannelcni-flannel:v0.19.2
        command:
        - cp
        args:
        - -f
        - /etc/kube-flannel/cni-conf.json
        - /etc/cni/net.d/10-flannel.conflist
        volumeMounts:
        - name: cni
          mountPath: /etc/cni/net.d
        - name: flannel-cfg
          mountPath: /etc/kube-flannel/
      containers:
      - name: kube-flannel
        image: rancher/mirrored-flannelcni-flannel:v0.19.2
        command:
        - /opt/bin/flanneld
        args:
        - --ip-masq
        - --kube-subnet-mgr
        resources:
          requests:
            cpu: "100m"
            memory: "50Mi"
          limits:
            cpu: "100m"
            memory: "50Mi"
        securityContext:
          privileged: false
          capabilities:
            add: ["NET_ADMIN", "NET_RAW"]
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: EVENT_QUEUE_DEPTH
          value: "5000"
        volumeMounts:
        - name: run
          mountPath: /run/flannel
        - name: flannel-cfg
          mountPath: /etc/kube-flannel/
        - name: xtables-lock
          mountPath: /run/xtables.lock
      volumes:
      - name: run
        hostPath:
          path: /run/flannel
      - name: cni-plugin
        hostPath:
          path: /opt/cni/bin
      - name: cni
        hostPath:
          path: /etc/cni/net.d
      - name: flannel-cfg
        configMap:
          name: kube-flannel-cfg
      - name: xtables-lock
        hostPath:
          path: /run/xtables.lock
          type: FileOrCreate
EOF

    # 尝试应用网络插件
    if kubectl apply -f flannel-offline.yaml; then
        log_success "网络插件安装完成"
    else
        log_warning "网络插件安装失败，集群可能无法正常工作"
        log_info "请手动配置网络插件或检查镜像是否可用"
    fi
}

# 显示结果
show_results() {
    echo
    echo "========================================"
    echo "           安装完成"
    echo "========================================"
    echo

    if [ "$NODE_TYPE" = "master" ]; then
        log_success "Master 节点配置完成！"
        echo

        if [ "$SINGLE_NODE" = "true" ]; then
            echo "单节点集群状态："
            kubectl get nodes 2>/dev/null || log_warning "无法获取节点状态，请稍后重试"
            echo
            log_success "单节点 Kubernetes 集群已就绪！"
            echo "可以直接部署应用，无需添加 Worker 节点。"
        else
            echo "集群状态："
            kubectl get nodes 2>/dev/null || log_warning "无法获取节点状态，请稍后重试"
            echo
            echo "Worker 节点加入命令："
            echo "$JOIN_COMMAND"
            echo
            echo "请在每个 Worker 节点上运行上述命令来加入集群"

            # 保存 join 命令到文件
            echo "$JOIN_COMMAND" > join-command.txt
            log_info "join 命令已保存到 join-command.txt"
        fi

    else
        log_success "Worker 节点基础配置完成！"
        echo
        echo "请从 Master 节点获取 join 命令并运行："
        echo "kubeadm join <master-ip>:6443 --token <token> --discovery-token-ca-cert-hash <hash>"
    fi

    echo
    echo "下一步："
    if [ "$SINGLE_NODE" = "true" ]; then
        echo "1. 验证集群状态: kubectl get nodes"
        echo "2. 检查 Pod 状态: kubectl get pods -A"
        echo "3. 部署 Dify: cd ../k8s-deployment && ./deploy.sh"
    else
        echo "1. 在所有 Worker 节点上运行 join 命令"
        echo "2. 验证集群状态: kubectl get nodes"
        echo "3. 检查 Pod 状态: kubectl get pods -A"
        echo "4. 部署 Dify: cd ../k8s-deployment && ./deploy.sh"
    fi

    echo
    log_info "离线安装注意事项："
    echo "- 如果网络插件 Pod 无法启动，请检查镜像是否可用"
    echo "- 可以使用 'kubectl describe pod <pod-name> -n kube-flannel' 查看详细信息"
    echo "- 必要时可以手动导入网络插件镜像"
}

# 主函数
main() {
    show_welcome

    printf "按 Enter 继续，或 Ctrl+C 退出..."
    read dummy
    echo

    check_environment
    get_configuration
    setup_system
    install_docker_offline
    install_kubernetes_offline
    init_master
    show_results
}

# 如果直接运行脚本
if [ "${0##*/}" = "offline-setup.sh" ]; then
    main "$@"
fi
